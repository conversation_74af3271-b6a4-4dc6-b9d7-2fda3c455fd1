import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import type { Claim } from "@/types";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { mockCarriers } from "@/pages/CarriersPage";
import { mockAdjusters } from "@/pages/AdjustersPage";
import { mockContractors } from "@/pages/ContractorsPage";

const STATUS_OPTIONS = [
  { value: "open", label: "Open" },
  { value: "in-progress", label: "In Progress" },
  { value: "under-Review", label: "Under Review" },
  { value: "approved", label: "Approved" },
  { value: "denied", label: "Denied" },
  { value: "closed", label: "Closed" },
];

const PRIORITY_OPTIONS = [
  { value: "low", label: "Low" },
  { value: "medium", label: "Medium" },
  { value: "high", label: "High" },
  { value: "critical", label: "Critical" },
]

export const claimFormSchema = z.object({
  id: z.string().optional(),
  // 1. Policy Information
  claimNumber: z.string().min(1, "Claim number is required"),
  policyNumber: z.string().min(1, "Policy number is required"),
  policyHolderName: z.string().min(1, "Policy holder name is required"),
  policyHolderPhone: z.string().min(1, "Policy holder phone is required"),
  policyHolderEmail: z.string().email("Invalid email").min(1, "Policy holder email is required"),
  carrierId: z.string().min(1, "Carrier is required"),
  // 2. Claim Details
  incidentDate: z.string().datetime().optional(),   // ISO string
  reportedDate: z.string().datetime().optional(),   // ISO string
  claimType: z.string().min(1, "Claim type is required"),
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().optional(),
  status: z.enum(['open', 'in-progress', 'under-review', 'approved', 'denied', 'closed']),
  priority: z.enum(['low', 'medium', 'high', 'critical']),
  description: z.string().min(1, "Description is required"),
  damageDescription: z.string().min(1, "Damage description is required"),
  // 3. Assignment y location
  adjusterId: z.string().optional(),
  contractorId: z.string().optional(),
  locationId: z.string().min(1, "Location is required").optional(),
  // 4. Financial details
  estimatedAmount: z.number(),
  approvedAmount: z.number().optional(),
  deductible: z.number(),
});

interface ClaimFormProps {
  initialData?: Claim;
  onCancel: () => void;
  onSubmit: (data: z.infer<typeof claimFormSchema>) => void;
}

export function ClaimForm({ initialData, onCancel, onSubmit }: ClaimFormProps) {
  const form = useForm<z.infer<typeof claimFormSchema>>({
    resolver: zodResolver(claimFormSchema),
    defaultValues: {
      id: initialData?.id || "",
      // 1. Policy Information
      claimNumber: initialData?.claimNumber || "",
      policyNumber: initialData?.policyNumber || "",
      policyHolderName: initialData?.policyHolderName || "",
      policyHolderPhone: initialData?.policyHolderPhone || "",
      policyHolderEmail: initialData?.policyHolderEmail || "",
      carrierId: initialData?.carrierId || "",
      //2. Claim Details
      incidentDate: initialData?.incidentDate || "",
      reportedDate: initialData?.reportedDate || "",
      claimType: initialData?.claimType || "",
      category: initialData?.category || "",
      subcategory: initialData?.subcategory || "",
      status: initialData?.status || "open",
      priority: initialData?.priority || "medium",
      description: initialData?.description || "",
      damageDescription: initialData?.damageDescription || "",
      // 3. Assignment & location
      adjusterId: initialData?.adjusterId || "",
      contractorId: initialData?.contractorId || "",
      locationId: initialData?.location.id || "",
      // 4. Financial details
      estimatedAmount: initialData?.estimatedAmount || 0,
      approvedAmount: initialData?.approvedAmount || 0,
      deductible: initialData?.deductible || 0,
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <Accordion type="multiple" defaultValue={["policy-information", "claim-details", "assignment-details", "financial-details"]}>

          {/* 1. Policy Information */}
          <AccordionItem value="policy-information">
            <AccordionTrigger>Policy Information</AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="claimNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Claim Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter claim number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="policyNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Policy Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter policy number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="policyHolderName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Policy Holder Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter policy holder name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="policyHolderPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Policy Holder Phone</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter policy holder phone" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="policyHolderEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Policy Holder Email</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter policy holder email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="carrierId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Carrier</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select carrier" />
                          </SelectTrigger>
                          <SelectContent>
                            {mockCarriers.map((carrier) => (
                              <SelectItem key={carrier.id} value={carrier.id}>
                                {carrier.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

            </AccordionContent>
          </AccordionItem>

          {/* 2. Claim Details */}
          <AccordionItem value="claim-details">
            <AccordionTrigger>Policy Information</AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="incidentDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Incident Date</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          value={field.value ? field.value.slice(0, 16) : ""}
                          onChange={(e) => {
                            const val = e.target.value;
                            // Convert local datetime to ISO
                            const isoString = new Date(val).toISOString();
                            field.onChange(isoString);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="reportedDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reported Date</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          value={field.value ? field.value.slice(0, 16) : ""}
                          onChange={(e) => {
                            const val = e.target.value;
                            // Convert local datetime to ISO
                            const isoString = new Date(val).toISOString();
                            field.onChange(isoString);
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="claimType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Claim Type</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter claim type" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter category" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="subcategory"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subcategory</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter subcategory" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            {STATUS_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Priority</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            {PRIORITY_OPTIONS.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter description" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="damageDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Damage Description</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter damage description" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* 3. Assignment Details */}
          <AccordionItem value="assignment-details">
            <AccordionTrigger>Assignment Details & Location</AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="adjusterId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Adjuster</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select adjuster" />
                          </SelectTrigger>
                          <SelectContent>
                            {mockAdjusters.map((adjuster) => (
                              <SelectItem key={adjuster.id} value={adjuster.id}>
                                {adjuster.firstName} {adjuster.lastName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="contractorId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contractor</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select contractor" />
                          </SelectTrigger>
                          <SelectContent>
                            {mockContractors.map((contractor) => (
                              <SelectItem key={contractor.id} value={contractor.id}>
                                {contractor.companyName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="locationId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter location" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* 4. Financial Details */}
          <AccordionItem value="financial-details">
            <AccordionTrigger>Financial Details</AccordionTrigger>
            <AccordionContent>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="estimatedAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estimated Amount</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter estimated amount"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="approvedAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Approved Amount</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter approved amount"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="deductible"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Deductible</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter deductible"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
          >
            Save
          </Button>
        </div>
      </form>
    </Form >
  )
}