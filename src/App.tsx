import './App.css'
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/context/ThemeContext";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ToastProvider } from "@/components/custom-ui/toast";
import { Layout } from '@/components/layout/Layout';
import { NotFound } from '@/pages/NotFoundPage';
import { ClaimPage } from '@/pages/ClaimPage';
import { Dashboard } from '@/pages/DashboardPage';
import { Carriers } from '@/pages/CarriersPage';
import { Adjusters } from '@/pages/AdjustersPage';
import { Contractors } from '@/pages/ContractorsPage';
import { Categories } from '@/pages/CategoriesPage';
import { Parameters } from '@/pages/Parameters';

function App() {
  return (
    <ThemeProvider>
      <TooltipProvider>
        <ToastProvider>
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Layout />}>
                <Route path="/" element={<Dashboard />} />
                <Route path="claims" element={<ClaimPage />} />
                <Route path="carriers" element={<Carriers />} />
                <Route path="adjusters" element={<Adjusters />} />
                <Route path="contractors" element={<Contractors />} />
                <Route path="categories" element={<Categories />} />
                <Route path="parameters" element={<Parameters />} />
                <Route path="*" element={<NotFound />} />
              </Route>
            </Routes>
          </BrowserRouter>
        </ToastProvider>
      </TooltipProvider>
    </ThemeProvider>
  )
}

export default App
