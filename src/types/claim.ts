export interface Claim {
  id: string;
  claimNumber: string;
  carrierId: string;
  adjusterId?: string;
  contractorId?: string;
  policyNumber: string;
  policyHolderName: string;
  policyHolderPhone: string;
  policyHolderEmail: string;
  incidentDate: string;
  reportedDate: string;
  claimType: string;
  category: string;
  subcategory?: string;
  status: 'open' | 'in-progress' | 'under-review' | 'approved' | 'denied' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  damageDescription: string;
  estimatedAmount: number;
  approvedAmount?: number;
  deductible: number;
  location: {
    id: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  assignedDate?: string; // ISO string
  completedDate?: string; // ISO string
  notes: ClaimNote[];
  documents: ClaimDocument[];
  createdAt: Date;
  updatedAt: Date;
}

interface ClaimNote {
  id: string;
  claimId: string;
  authorId: string;
  authorName: string;
  content: string;
  isInternal: boolean;
  createdAt: Date;
}

interface ClaimDocument {
  id: string;
  claimId: string;
  name: string;
  type: string;
  url: string;
  uploadedBy: string;
  uploadedAt: Date;
}