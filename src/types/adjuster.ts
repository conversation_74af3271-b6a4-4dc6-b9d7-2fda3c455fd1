export interface Adjuster {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  specializations: string[];
  assignedCarriers: string[]; // Carrier IDs
  licenseNumber: string;
  licenseExpiry: Date;
  isActive: boolean;
  rating: number; // 1-5 stars
  totalAssignments: number;
  avgCompletionTime: number; // in days
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  createdAt: Date;
  updatedAt: Date;
}