export interface Carrier {
  id: string;
  name: string;
  code: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  policyTypes: string[];
  isActive: boolean;
  rating: number; // 1-5 stars
  totalClaims: number;
  avgProcessingTime: number; // in days
  createdAt: Date;
  updatedAt: Date;
}