export interface Contractor {
  id: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  licenseNumber: string;
  licenseExpiry: Date;
  servicesOffered: string[];
  specializations: string[];
  rating: number; // 1-5 stars
  totalJobs: number;
  avgCompletionTime: number; // in days
  isActive: boolean;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  serviceArea: string[]; // List of cities/regions they serve
  insurance: {
    provider: string;
    policyNumber: string;
    expiryDate: Date;
    coverageAmount: number;
  };
  createdAt: Date;
  updatedAt: Date;
}