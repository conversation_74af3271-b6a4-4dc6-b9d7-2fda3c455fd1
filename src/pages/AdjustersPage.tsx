import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/custom-ui/badge';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Plus, Edit, Trash2, Star, Phone, Mail, Calendar } from 'lucide-react';
import { format } from 'date-fns';
import type { Adjuster } from '@/types';
import { AdjusterForm } from '@/components/pages/adjuster/AdjusterForm';

// Mock Data
import { mockCarriers } from '@/pages/CarriersPage';
import { v4 as uuidv4 } from 'uuid';
export const mockAdjusters: Adjuster[] = [
  {
    id: uuidv4(),
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    specializations: ["Auto Claims", "Property Damage", "Liability"],
    assignedCarriers: [mockCarriers[0].id, mockCarriers[1].id],
    licenseNumber: "ADJ-12345",
    licenseExpiry: new Date('2025-12-31'),
    isActive: true,
    rating: 4.8,
    totalAssignments: 156,
    avgCompletionTime: 8,
    address: {
      street: "123 Main St",
      city: "Chicago",
      state: "IL",
      zipCode: "60601"
    },
    emergencyContact: {
      name: "James Carter",
      phone: "(*************",
      relationship: "Spouse"
    },
    createdAt: new Date('2021-03-15'),
    updatedAt: new Date('2024-06-20')
  },
  {
    id: uuidv4(),
    firstName: "Marcus",
    lastName: "Johnson",
    email: "<EMAIL>",
    phone: "(*************",
    specializations: ["Commercial Claims", "Workers Compensation", "Fraud Investigation"],
    assignedCarriers: [mockCarriers[2].id, mockCarriers[3].id],
    licenseNumber: "ADJ-23456",
    licenseExpiry: new Date('2026-06-30'),
    isActive: true,
    rating: 4.6,
    totalAssignments: 203,
    avgCompletionTime: 12,
    address: {
      street: "456 Oak Ave",
      city: "Cleveland",
      state: "OH",
      zipCode: "44101"
    },
    emergencyContact: {
      name: "Maria Johnson",
      phone: "(*************",
      relationship: "Sister"
    },
    createdAt: new Date('2020-08-20'),
    updatedAt: new Date('2024-06-18')
  },
  {
    id: uuidv4(),
    firstName: "Sarah",
    lastName: "Williams",
    email: "<EMAIL>",
    phone: "(*************",
    specializations: ["Property Claims", "Water Damage", "Fire Damage"],
    assignedCarriers: [mockCarriers[0].id, mockCarriers[4].id],
    licenseNumber: "ADJ-34567",
    licenseExpiry: new Date('2025-09-15'),
    isActive: true,
    rating: 4.9,
    totalAssignments: 189,
    avgCompletionTime: 7,
    address: {
      street: "789 Pine St",
      city: "Boston",
      state: "MA",
      zipCode: "02101"
    },
    emergencyContact: {
      name: "David Williams",
      phone: "(*************",
      relationship: "Father"
    },
    createdAt: new Date('2021-01-10'),
    updatedAt: new Date('2024-06-22')
  }
];

export function Adjusters() {
  const [adjusters, setAdjusters] = useState<Adjuster[]>(mockAdjusters);
  const [isAdjusterFormDialogOpen, setIsAdjusterFormDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingAdjuster, setEditingAdjuster] = useState<Adjuster | null>(null);
  const [deletingAdjuster, setDeletingAdjuster] = useState<Adjuster | null>(null);

  const columns: Column<Adjuster>[] = [
    {
      key: 'firstName',
      header: 'Name',
      sortable: true,
      render: (_, row) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
            <span className="font-semibold text-primary">
              {row.firstName[0]}{row.lastName[0]}
            </span>
          </div>
          <div>
            <div className="font-medium">{row.firstName} {row.lastName}</div>
            <div className="text-sm text-muted-foreground">{row.licenseNumber}</div>
          </div>
        </div>
      )
    },
    {
      key: 'email',
      header: 'Contact',
      render: (value, row) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-3 w-3" />
            {value}
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Phone className="h-3 w-3" />
            {row.phone}
          </div>
        </div>
      )
    },
    {
      key: 'specializations',
      header: 'Specializations',
      render: (value: string[]) => (
        <div className="flex flex-wrap gap-1">
          {value.slice(0, 2).map((spec) => (
            <Badge key={spec} variant="secondary" className="text-xs">
              {spec}
            </Badge>
          ))}
          {value.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{value.length - 2}
            </Badge>
          )}
        </div>
      )
    },
    {
      key: 'rating',
      header: 'Rating',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="font-medium">{value}</span>
        </div>
      )
    },
    {
      key: 'totalAssignments',
      header: 'Assignments',
      sortable: true,
      render: (value) => (
        <span className="font-medium">{value.toLocaleString()}</span>
      )
    },
    {
      key: 'avgCompletionTime',
      header: 'Avg. Time',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{value} days</span>
      )
    },
    {
      key: 'licenseExpiry',
      header: 'License Expiry',
      render: (value) => (
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="h-3 w-3" />
          {format(new Date(value), 'MMM dd, yyyy')}
        </div>
      )
    },
    {
      key: 'isActive',
      header: 'Status',
      render: (value) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setEditingAdjuster(row);
              setIsAdjusterFormDialogOpen(true);
            }}
            className='cursor-pointer'
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setDeletingAdjuster(row);
              setIsDeleteDialogOpen(true);
            }}
            className='cursor-pointer'
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleSave = (formData: any) => {
    setAdjusters(prev => [...prev, { ...formData, id: uuidv4() }]);
    setIsAdjusterFormDialogOpen(false);
    console.log('Form data: ', formData);
  }

  const handleEdit = (formData: any) => {
    console.log('Edit adjuster: ', formData);
    setAdjusters(adjusters.map(a =>
      a.id === formData.id ? { ...a, ...formData } : a
    ));
    setIsAdjusterFormDialogOpen(false);
  };

  const handleDelete = (adjuster: Adjuster) => {
    setAdjusters(adjusters.filter(a => a.id !== adjuster.id));
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Adjusters"
        description="Manage insurance adjusters and their assignments"
      >
        <Button onClick={() => {
          setEditingAdjuster(null);
          setIsAdjusterFormDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Add Adjuster
        </Button>
      </PageHeader>

      <PageContent>
        <DataTable
          data={adjusters}
          columns={columns}
          searchPlaceholder="Search adjusters..."
          onRowClick={(adjuster) => console.log('View adjuster:', adjuster)}
        />
      </PageContent>

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingAdjuster ? 'Edit Adjuster' : 'Add New Adjuster'}
        size="lg"
        footer={
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {editingAdjuster ? 'Update' : 'Create'}
            </Button>
          </div>
        }
      >
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              value={formData.firstName || ''}
              onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
              placeholder="Enter first name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              value={formData.lastName || ''}
              onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
              placeholder="Enter last name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email || ''}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="Enter email address"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={formData.phone || ''}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              placeholder="Enter phone number"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="licenseNumber">License Number</Label>
            <Input
              id="licenseNumber"
              value={formData.licenseNumber || ''}
              onChange={(e) => setFormData({ ...formData, licenseNumber: e.target.value })}
              placeholder="Enter license number"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="rating">Rating</Label>
            <Input
              id="rating"
              type="number"
              min="0"
              max="5"
              step="0.1"
              value={formData.rating || ''}
              onChange={(e) => setFormData({ ...formData, rating: parseFloat(e.target.value) })}
              placeholder="Enter rating (0-5)"
            />
          </div>
          <div className="flex items-center space-x-2 col-span-2">
            <Switch
              id="isActive"
              checked={formData.isActive ?? true}
              onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
            />
            <Label htmlFor="isActive">Active</Label>
          </div>
        </div>
      </Modal>
    </div>
  );
}
