export interface DashboardMetrics {
  openClaims: number;
  closedClaims: number;
  activeConsultations: number;
  totalRevenue: number;
  avgProcessingTime: number;
  carrierPerformance: CarrierPerformance[];
  recentActivity: ActivityItem[];
  claimsByStatus: ClaimStatusCount[];
  claimsByMonth: MonthlyClaimData[];
  topAdjusters: AdjusterPerformance[];
}

export interface CarrierPerformance {
  carrierId: string;
  carrierName: string;
  totalClaims: number;
  avgProcessingTime: number;
  satisfactionRating: number;
  revenue: number;
}

export interface ActivityItem {
  id: string;
  type: 'claim_created' | 'claim_assigned' | 'claim_completed' | 'adjuster_added' | 'carrier_updated';
  title: string;
  description: string;
  timestamp: Date;
  userId?: string;
  userName?: string;
}

export interface ClaimStatusCount {
  status: string;
  count: number;
  percentage: number;
}

export interface MonthlyClaimData {
  month: string;
  openClaims: number;
  closedClaims: number;
  revenue: number;
}

export interface AdjusterPerformance {
  adjusterId: string;
  adjusterName: string;
  totalClaims: number;
  avgCompletionTime: number;
  rating: number;
  revenue: number;
}

export type DateRange = 'daily' | 'weekly' | 'monthly' | 'quarterly';
