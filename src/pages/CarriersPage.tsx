import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/custom-ui/badge';
import { Di<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Plus, Edit, Trash2, Star, Phone, Mail, MapPin } from 'lucide-react';
import type { Carrier } from '@/types';
import { CarrierForm } from '@/components/pages/carrier/CarrierForm';

// Mock Data
import { v4 as uuidv4 } from 'uuid';
export const mockCarriers: Carrier[] = [
  {
    id: uuidv4(),
    name: "State Farm Insurance",
    code: "SF",
    contactPerson: "<PERSON> Mitchell",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "1 State Farm Plaza",
      city: "Bloomington",
      state: "IL",
      zipCode: "61710"
    },
    policyTypes: ["Auto", "Home", "Life", "Commercial"],
    isActive: true,
    rating: 4.5,
    totalClaims: 1247,
    avgProcessingTime: 12,
    createdAt: new Date('2020-01-15'),
    updatedAt: new Date('2024-06-15')
  },
  {
    id: uuidv4(),
    name: "Allstate Insurance",
    code: "AS",
    contactPerson: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "2775 Sanders Rd",
      city: "Northbrook",
      state: "IL",
      zipCode: "60062"
    },
    policyTypes: ["Auto", "Home", "Renters", "Commercial"],
    isActive: true,
    rating: 4.2,
    totalClaims: 892,
    avgProcessingTime: 15,
    createdAt: new Date('2020-03-20'),
    updatedAt: new Date('2024-06-10')
  },
  {
    id: uuidv4(),
    name: "Progressive Insurance",
    code: "PG",
    contactPerson: "Michael Chen",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "6300 Wilson Mills Rd",
      city: "Mayfield Village",
      state: "OH",
      zipCode: "44143"
    },
    policyTypes: ["Auto", "Motorcycle", "Commercial Auto"],
    isActive: true,
    rating: 4.0,
    totalClaims: 1156,
    avgProcessingTime: 10,
    createdAt: new Date('2020-05-10'),
    updatedAt: new Date('2024-06-20')
  },
  {
    id: uuidv4(),
    name: "GEICO Insurance",
    code: "GC",
    contactPerson: "Lisa Rodriguez",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "1 GEICO Plaza",
      city: "Chevy Chase",
      state: "MD",
      zipCode: "20815"
    },
    policyTypes: ["Auto", "Motorcycle", "Home", "Renters"],
    isActive: true,
    rating: 4.3,
    totalClaims: 2134,
    avgProcessingTime: 8,
    createdAt: new Date('2019-11-05'),
    updatedAt: new Date('2024-06-25')
  },
  {
    id: uuidv4(),
    name: "Liberty Mutual",
    code: "LM",
    contactPerson: "David Thompson",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "175 Berkeley St",
      city: "Boston",
      state: "MA",
      zipCode: "02116"
    },
    policyTypes: ["Auto", "Home", "Life", "Commercial", "Workers Comp"],
    isActive: true,
    rating: 4.1,
    totalClaims: 756,
    avgProcessingTime: 14,
    createdAt: new Date('2021-02-12'),
    updatedAt: new Date('2024-06-18')
  },
  {
    id: uuidv4(),
    name: "Farmers Insurance",
    code: "FI",
    contactPerson: "Amanda Wilson",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "4680 Wilshire Blvd",
      city: "Los Angeles",
      state: "CA",
      zipCode: "90010"
    },
    policyTypes: ["Auto", "Home", "Life", "Business"],
    isActive: true,
    rating: 3.9,
    totalClaims: 634,
    avgProcessingTime: 16,
    createdAt: new Date('2021-08-30'),
    updatedAt: new Date('2024-06-12')
  },
  {
    id: uuidv4(),
    name: "Nationwide Insurance",
    code: "NW",
    contactPerson: "Robert Garcia",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "1 Nationwide Plaza",
      city: "Columbus",
      state: "OH",
      zipCode: "43215"
    },
    policyTypes: ["Auto", "Home", "Life", "Pet", "Commercial"],
    isActive: false,
    rating: 4.4,
    totalClaims: 423,
    avgProcessingTime: 11,
    createdAt: new Date('2022-01-15'),
    updatedAt: new Date('2024-05-20')
  }
];

export function Carriers() {
  const [carriers, setCarriers] = useState<Carrier[]>(mockCarriers);
  const [isCarrierFormDialogOpen, setIsCarrierFormDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingCarrier, setEditingCarrier] = useState<Carrier | null>(null);
  const [deletingCarrier, setDeletingCarrier] = useState<Carrier | null>(null);

  const columns: Column<Carrier>[] = [
    {
      key: 'name',
      header: 'Company Name',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
            <span className="font-semibold text-primary">{row.code}</span>
          </div>
          <div>
            <div className="font-medium">{value}</div>
            <div className="text-sm text-muted-foreground">{row.contactPerson}</div>
          </div>
        </div>
      )
    },
    {
      key: 'email',
      header: 'Contact',
      render: (value, row) => (
        <div className="space-y-1 max-w-[150px]">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-3 w-3" />
            <span className="truncate whitespace-nowrap overflow-hidden block">{value}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Phone className="h-3 w-3" />
            <span className="truncate whitespace-nowrap overflow-hidden block">{row.phone}</span>
          </div>
        </div>
      )
    },
    {
      key: 'address',
      header: 'Location',
      render: (value) => (
        <div className="flex items-center gap-2 text-sm">
          <MapPin className="h-3 w-3" />
          {value.city}, {value.state}
        </div>
      )
    },
    {
      key: 'policyTypes',
      header: 'Policy Types',
      render: (value: string[]) => (
        <div className="flex flex-wrap gap-1">
          {value.slice(0, 2).map((type) => (
            <Badge key={type} variant="secondary" className="text-xs">
              {type}
            </Badge>
          ))}
          {value.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{value.length - 2}
            </Badge>
          )}
        </div>
      )
    },
    {
      key: 'rating',
      header: 'Rating',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="font-medium">{value}</span>
        </div>
      )
    },
    {
      key: 'totalClaims',
      header: 'Total Claims',
      sortable: true,
      render: (value) => (
        <span className="font-medium">{value.toLocaleString()}</span>
      )
    },
    {
      key: 'isActive',
      header: 'Status',
      render: (value) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setEditingCarrier(row);
              setIsCarrierFormDialogOpen(true);
            }}
            className='cursor-pointer'
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setDeletingCarrier(row);
              setIsDeleteDialogOpen(true);
            }}
            className='cursor-pointer'
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleSave = (formData: any) => {
    setCarriers(prev => [...prev, { ...formData, id: uuidv4() }]);
    setIsCarrierFormDialogOpen(false);
    console.log('Form data: ', formData);
  }

  const handleEdit = (formData: any) => {
    console.log('Edit carrier: ', formData);
    setCarriers(carriers.map(c =>
      c.id === formData.id ? { ...c, ...formData } : c
    ));
    setIsCarrierFormDialogOpen(false);
  };

  const handleDelete = (carrier: Carrier) => {
    setCarriers(carriers.filter(c => c.id !== carrier.id));
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Carriers"
        description="Manage insurance carriers and their information"
      >
        <Button onClick={() => {
          setEditingCarrier(null);
          setIsCarrierFormDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Add Carrier
        </Button>
      </PageHeader>

      <PageContent>
        <DataTable
          data={carriers}
          columns={columns}
          searchPlaceholder="Search carriers..."
          onRowClick={(carrier) => console.log('View carrier:', carrier)}
        />
      </PageContent>

      {/* Add/Edit Dialog */}
      <Dialog open={isCarrierFormDialogOpen} onOpenChange={setIsCarrierFormDialogOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {editingCarrier ? 'Edit Carrier' : 'Add New Carrier'}
            </DialogTitle>
          </DialogHeader>
          {
            editingCarrier ? (
              <CarrierForm
                initialData={editingCarrier}
                onCancel={() => setIsCarrierFormDialogOpen(false)}
                onSubmit={handleEdit}
              />
            ) : (
              <CarrierForm
                onCancel={() => setIsCarrierFormDialogOpen(false)}
                onSubmit={handleSave}
              />
            )
          }
        </DialogContent>
      </Dialog>

      {/* Confirm Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              Delete Carrier
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete this carrier?</p>
          </div>
          <DialogFooter>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => {
                setDeletingCarrier(null);
                setIsDeleteDialogOpen(false)
              }}>
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => {
                  if (deletingCarrier) handleDelete(deletingCarrier);
                  setIsDeleteDialogOpen(false);
                }}
              >
                Delete
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
