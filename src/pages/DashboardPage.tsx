import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/custom-ui/page-header';
import { MetricCard, MetricGrid } from '@/components/custom-ui/metric-card';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import {
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import {
  ClipboardList,
  CheckCircle,
  Users,
  DollarSign,
  Filter
} from 'lucide-react';
import { format } from 'date-fns';
import type { DateRange, DashboardMetrics } from '@/pages/types/dashboard';

// Mock Data
import { v4 as uuidv4 } from 'uuid';
import { mockCarriers } from '@/pages/CarriersPage';
import { mockAdjusters } from '@/pages/AdjustersPage';
export const mockDashboardMetrics: DashboardMetrics = {
  openClaims: 156,
  closedClaims: 1247,
  activeConsultations: 89,
  totalRevenue: 2456789,
  avgProcessingTime: 11.5,
  carrierPerformance: [
    {
      carrierId: mockCarriers[0].id,
      carrierName: mockCarriers[0].name,
      totalClaims: 1247,
      avgProcessingTime: 12,
      satisfactionRating: 4.5,
      revenue: 856432
    },
    {
      carrierId: mockCarriers[1].id,
      carrierName: mockCarriers[1].name,
      totalClaims: 892,
      avgProcessingTime: 15,
      satisfactionRating: 4.2,
      revenue: 634521
    }
  ],
  recentActivity: [
    {
      id: uuidv4(),
      type: "claim_created",
      title: "New Claim Created",
      description: "CLM-2024-001234 - Auto collision claim",
      timestamp: new Date('2024-06-20T10:30:00'),
      userId: "user-1",
      userName: "Emily Carter"
    },
    {
      id: uuidv4(),
      type: "claim_assigned",
      title: "Claim Assigned",
      description: "CLM-2024-001235 assigned to Marcus Johnson",
      timestamp: new Date('2024-06-20T09:15:00'),
      userId: "user-2",
      userName: "System"
    }
  ],
  claimsByStatus: [
    { status: "Open", count: 156, percentage: 35 },
    { status: "In Progress", count: 89, percentage: 20 },
    { status: "Under Review", count: 67, percentage: 15 },
    { status: "Closed", count: 134, percentage: 30 }
  ],
  claimsByMonth: [
    { month: "Jan", openClaims: 45, closedClaims: 38, revenue: 185000 },
    { month: "Feb", openClaims: 52, closedClaims: 41, revenue: 198000 },
    { month: "Mar", openClaims: 48, closedClaims: 45, revenue: 210000 },
    { month: "Apr", openClaims: 61, closedClaims: 52, revenue: 225000 },
    { month: "May", openClaims: 58, closedClaims: 49, revenue: 215000 },
    { month: "Jun", openClaims: 67, closedClaims: 55, revenue: 245000 }
  ],
  topAdjusters: [
    {
      adjusterId: mockAdjusters[0].id,
      adjusterName: `${mockAdjusters[0].firstName} ${mockAdjusters[0].lastName}`,
      totalClaims: 156,
      avgCompletionTime: 8,
      rating: 4.8,
      revenue: 425000
    },
    {
      adjusterId: mockAdjusters[1].id,
      adjusterName: `${mockAdjusters[1].firstName} ${mockAdjusters[1].lastName}`,
      totalClaims: 203,
      avgCompletionTime: 12,
      rating: 4.6,
      revenue: 567000
    }
  ]
};

export function Dashboard() {
  const [dateRange, setDateRange] = useState<DateRange>('monthly');

  const metrics = mockDashboardMetrics;
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Dashboard"
        description="Overview of claims, performance metrics, and key insights"
      >
        <div className="flex items-center gap-4">
          <Select value={dateRange} onValueChange={(value: DateRange) => setDateRange(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>
      </PageHeader>

      <PageContent>
        {/* Key Metrics */}
        <MetricGrid className="mb-8">
          <MetricCard
            title="Open Claims"
            value={formatNumber(metrics.openClaims)}
            change={{ value: 12, type: 'increase', period: 'last month' }}
            icon={ClipboardList}
            trend="up"
          />
          <MetricCard
            title="Closed Claims"
            value={formatNumber(metrics.closedClaims)}
            change={{ value: 8, type: 'increase', period: 'last month' }}
            icon={CheckCircle}
            trend="up"
          />
          <MetricCard
            title="Active Consultations"
            value={formatNumber(metrics.activeConsultations)}
            change={{ value: 3, type: 'decrease', period: 'last week' }}
            icon={Users}
            trend="down"
          />
          <MetricCard
            title="Total Revenue"
            value={formatCurrency(metrics.totalRevenue)}
            change={{ value: 15, type: 'increase', period: 'last quarter' }}
            icon={DollarSign}
            trend="up"
          />
        </MetricGrid>

        <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {/* Claims by Status */}
          <Card className="col-span-1 md:col-span-1 lg:col-span-1">
            <CardHeader>
              <CardTitle>Claims by Status</CardTitle>
              <CardDescription>Distribution of current claim statuses</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={metrics.claimsByStatus}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percentage }) => `${name} ${percentage}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {metrics.claimsByStatus.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Monthly Claims Trend */}
          <Card className="col-span-1 md:col-span-2 lg:col-span-2">
            <CardHeader>
              <CardTitle>Claims Trend</CardTitle>
              <CardDescription>Monthly open vs closed claims</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={metrics.claimsByMonth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value, name) => [formatNumber(Number(value)), name]} />
                  <Area
                    type="monotone"
                    dataKey="openClaims"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    name="Open Claims"
                  />
                  <Area
                    type="monotone"
                    dataKey="closedClaims"
                    stackId="1"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    name="Closed Claims"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Revenue Trend */}
          <Card className="col-span-1 md:col-span-2 lg:col-span-2">
            <CardHeader>
              <CardTitle>Revenue Trend</CardTitle>
              <CardDescription>Monthly revenue performance</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={metrics.claimsByMonth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => formatCurrency(value)} />
                  <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Revenue']} />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="#ff7300"
                    strokeWidth={3}
                    dot={{ fill: '#ff7300' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Top Adjusters */}
          <Card className="col-span-1 md:col-span-1 lg:col-span-1">
            <CardHeader>
              <CardTitle>Top Adjusters</CardTitle>
              <CardDescription>Performance by adjuster</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.topAdjusters.map((adjuster, index) => (
                  <div key={adjuster.adjusterId} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{adjuster.adjusterName}</p>
                        <p className="text-sm text-muted-foreground">
                          {adjuster.totalClaims} claims
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(adjuster.revenue)}</p>
                      <p className="text-sm text-muted-foreground">
                        ⭐ {adjuster.rating}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates and actions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {metrics.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start gap-4 p-4 rounded-lg border">
                  <div className="w-2 h-2 rounded-full bg-primary mt-2"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{activity.title}</h4>
                      <span className="text-sm text-muted-foreground">
                        {format(activity.timestamp, 'MMM dd, HH:mm')}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {activity.description}
                    </p>
                    {activity.userName && (
                      <p className="text-xs text-muted-foreground mt-1">
                        by {activity.userName}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </PageContent>
    </div>
  );
}
