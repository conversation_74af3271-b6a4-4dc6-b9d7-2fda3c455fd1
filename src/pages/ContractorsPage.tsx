import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/custom-ui/badge';
import { Modal } from '@/components/custom-ui/modal';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Plus, Edit, Trash2, Star, Phone, Mail, MapPin, Shield } from 'lucide-react';
import type { Contractor } from '@/types';

// Mock Data
import { v4 as uuidv4 } from 'uuid';
export const mockContractors: Contractor[] = [
  {
    id: uuidv4(),
    companyName: "Elite Auto Repair",
    contactPerson: "<PERSON> Russo",
    email: "<EMAIL>",
    phone: "(*************",
    licenseNumber: "CONT-11111",
    licenseExpiry: new Date('2025-12-31'),
    servicesOffered: ["Auto Body Repair", "Mechanical Repair", "Towing"],
    specializations: ["Collision Repair", "Paint Work", "Frame Straightening"],
    rating: 4.7,
    totalJobs: 342,
    avgCompletionTime: 5,
    isActive: true,
    address: {
      street: "1234 Industrial Blvd",
      city: "Chicago",
      state: "IL",
      zipCode: "60622"
    },
    serviceArea: ["Chicago", "Evanston", "Oak Park", "Cicero"],
    insurance: {
      provider: "Commercial Insurance Co",
      policyNumber: "CIC-789456",
      expiryDate: new Date('2025-06-30'),
      coverageAmount: 2000000
    },
    createdAt: new Date('2021-05-15'),
    updatedAt: new Date('2024-06-20')
  },
  {
    id: uuidv4(),
    companyName: "Precision Property Restoration",
    contactPerson: "Jennifer Lee",
    email: "<EMAIL>",
    phone: "(*************",
    licenseNumber: "CONT-22222",
    licenseExpiry: new Date('2026-03-15'),
    servicesOffered: ["Water Damage Restoration", "Fire Damage Repair", "Mold Remediation"],
    specializations: ["Emergency Response", "Structural Drying", "Content Restoration"],
    rating: 4.9,
    totalJobs: 156,
    avgCompletionTime: 8,
    isActive: true,
    address: {
      street: "567 Restoration Way",
      city: "Boston",
      state: "MA",
      zipCode: "02118"
    },
    serviceArea: ["Boston", "Cambridge", "Somerville", "Newton"],
    insurance: {
      provider: "Restoration Insurance Group",
      policyNumber: "RIG-456789",
      expiryDate: new Date('2025-09-30'),
      coverageAmount: 5000000
    },
    createdAt: new Date('2020-11-20'),
    updatedAt: new Date('2024-06-15')
  },
  {
    id: uuidv4(),
    companyName: "Metro Glass & Glazing",
    contactPerson: "Carlos Martinez",
    email: "<EMAIL>",
    phone: "(*************",
    licenseNumber: "CONT-33333",
    licenseExpiry: new Date('2025-08-20'),
    servicesOffered: ["Glass Replacement", "Window Repair", "Windshield Replacement"],
    specializations: ["Commercial Glass", "Residential Windows", "Auto Glass"],
    rating: 4.4,
    totalJobs: 289,
    avgCompletionTime: 2,
    isActive: true,
    address: {
      street: "890 Glass Street",
      city: "Cleveland",
      state: "OH",
      zipCode: "44115"
    },
    serviceArea: ["Cleveland", "Akron", "Canton", "Youngstown"],
    insurance: {
      provider: "Glazier's Insurance",
      policyNumber: "GI-123456",
      expiryDate: new Date('2025-12-15'),
      coverageAmount: 1500000
    },
    createdAt: new Date('2021-09-10'),
    updatedAt: new Date('2024-06-18')
  }
];

export function Contractors() {
  const [contractors, setContractors] = useState<Contractor[]>(mockContractors);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingContractor, setEditingContractor] = useState<Contractor | null>(null);
  const [formData, setFormData] = useState<Partial<Contractor>>({});

  const columns: Column<Contractor>[] = [
    {
      key: 'companyName',
      header: 'Company',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
            <span className="font-semibold text-primary text-xs">
              {value.split(' ').map((w: string) => w[0]).join('').slice(0, 2)}
            </span>
          </div>
          <div>
            <div className="font-medium">{value}</div>
            <div className="text-sm text-muted-foreground">{row.contactPerson}</div>
          </div>
        </div>
      )
    },
    {
      key: 'email',
      header: 'Contact',
      render: (value, row) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-3 w-3" />
            {value}
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Phone className="h-3 w-3" />
            {row.phone}
          </div>
        </div>
      )
    },
    {
      key: 'servicesOffered',
      header: 'Services',
      render: (value: string[]) => (
        <div className="flex flex-wrap gap-1">
          {value.slice(0, 2).map((service) => (
            <Badge key={service} variant="secondary" className="text-xs">
              {service}
            </Badge>
          ))}
          {value.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{value.length - 2}
            </Badge>
          )}
        </div>
      )
    },
    {
      key: 'serviceArea',
      header: 'Service Area',
      render: (value: string[]) => (
        <div className="flex items-center gap-2 text-sm">
          <MapPin className="h-3 w-3" />
          {value.slice(0, 2).join(', ')}
          {value.length > 2 && ` +${value.length - 2}`}
        </div>
      )
    },
    {
      key: 'rating',
      header: 'Rating',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="font-medium">{value}</span>
        </div>
      )
    },
    {
      key: 'totalJobs',
      header: 'Total Jobs',
      sortable: true,
      render: (value) => (
        <span className="font-medium">{value.toLocaleString()}</span>
      )
    },
    {
      key: 'avgCompletionTime',
      header: 'Avg. Time',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{value} days</span>
      )
    },
    {
      key: 'insurance',
      header: 'Insurance',
      render: (value) => (
        <div className="flex items-center gap-2 text-sm">
          <Shield className="h-3 w-3" />
          <span className="text-xs">
            ${(value.coverageAmount / 1000000).toFixed(1)}M
          </span>
        </div>
      )
    },
    {
      key: 'isActive',
      header: 'Status',
      render: (value) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDelete(row.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleAdd = () => {
    setEditingContractor(null);
    setFormData({});
    setIsModalOpen(true);
  };

  const handleEdit = (contractor: Contractor) => {
    setEditingContractor(contractor);
    setFormData(contractor);
    setIsModalOpen(true);
  };

  const handleDelete = (id: string) => {
    setContractors(contractors.filter(c => c.id !== id));
  };

  const handleSave = () => {
    if (editingContractor) {
      // Update existing contractor
      setContractors(contractors.map(c =>
        c.id === editingContractor.id ? { ...c, ...formData } : c
      ));
    } else {
      // Add new contractor
      const newContractor: Contractor = {
        id: Date.now().toString(),
        companyName: formData.companyName || '',
        contactPerson: formData.contactPerson || '',
        email: formData.email || '',
        phone: formData.phone || '',
        licenseNumber: formData.licenseNumber || '',
        licenseExpiry: formData.licenseExpiry || new Date(),
        servicesOffered: formData.servicesOffered || [],
        specializations: formData.specializations || [],
        rating: formData.rating || 0,
        totalJobs: formData.totalJobs || 0,
        avgCompletionTime: formData.avgCompletionTime || 0,
        isActive: formData.isActive ?? true,
        address: formData.address || {
          street: '',
          city: '',
          state: '',
          zipCode: ''
        },
        serviceArea: formData.serviceArea || [],
        insurance: formData.insurance || {
          provider: '',
          policyNumber: '',
          expiryDate: new Date(),
          coverageAmount: 0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setContractors([...contractors, newContractor]);
    }
    setIsModalOpen(false);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Contractors"
        description="Manage contractors and service providers"
      >
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Contractor
        </Button>
      </PageHeader>

      <PageContent>
        <DataTable
          data={contractors}
          columns={columns}
          searchPlaceholder="Search contractors..."
          onRowClick={(contractor) => console.log('View contractor:', contractor)}
        />
      </PageContent>

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingContractor ? 'Edit Contractor' : 'Add New Contractor'}
        size="lg"
        footer={
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {editingContractor ? 'Update' : 'Create'}
            </Button>
          </div>
        }
      >
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="companyName">Company Name</Label>
            <Input
              id="companyName"
              value={formData.companyName || ''}
              onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
              placeholder="Enter company name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="contactPerson">Contact Person</Label>
            <Input
              id="contactPerson"
              value={formData.contactPerson || ''}
              onChange={(e) => setFormData({ ...formData, contactPerson: e.target.value })}
              placeholder="Enter contact person name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email || ''}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="Enter email address"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={formData.phone || ''}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              placeholder="Enter phone number"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="licenseNumber">License Number</Label>
            <Input
              id="licenseNumber"
              value={formData.licenseNumber || ''}
              onChange={(e) => setFormData({ ...formData, licenseNumber: e.target.value })}
              placeholder="Enter license number"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="rating">Rating</Label>
            <Input
              id="rating"
              type="number"
              min="0"
              max="5"
              step="0.1"
              value={formData.rating || ''}
              onChange={(e) => setFormData({ ...formData, rating: parseFloat(e.target.value) })}
              placeholder="Enter rating (0-5)"
            />
          </div>
          <div className="flex items-center space-x-2 col-span-2">
            <Switch
              id="isActive"
              checked={formData.isActive ?? true}
              onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
            />
            <Label htmlFor="isActive">Active</Label>
          </div>
        </div>
      </Modal>
    </div>
  );
}
