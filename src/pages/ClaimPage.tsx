import { useState } from 'react';
import { Plus, Edit, Trash2, DollarSign, Calendar, User, Building2 } from 'lucide-react';
import { format } from 'date-fns';
import { PageHeader, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/custom-ui/badge';
import { Modal } from '@/components/custom-ui/modal';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ClaimForm } from '@/components/pages/claim/ClaimForm';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import type { Claim } from '@/types';

// Mock Data
import { mockCarriers } from '@/pages/CarriersPage';
import { mockAdjusters } from '@/pages/AdjustersPage';
import { mockContractors } from '@/pages/ContractorsPage';
import { v4 as uuidv4 } from 'uuid';
export const mockClaims: Claim[] = [
  {
    id: uuidv4(),
    claimNumber: "CLM-2024-001234",
    carrierId: mockCarriers[0].id,
    adjusterId: mockAdjusters[0].id,
    contractorId: mockContractors[0].id,
    policyNumber: "SF-POL-789456",
    policyHolderName: "Robert Anderson",
    policyHolderPhone: "(*************",
    policyHolderEmail: "<EMAIL>",
    incidentDate: new Date('2024-06-15').toISOString(),
    reportedDate: new Date('2024-06-16').toISOString(),
    claimType: "Auto",
    category: "Collision",
    status: "open",
    priority: "low",
    description: "Rear-end collision at intersection",
    damageDescription: "Significant rear bumper damage, trunk damage, possible frame damage",
    estimatedAmount: 8500,
    approvedAmount: 7800,
    deductible: 500,
    location: {
      id: uuidv4(),
      street: "Main St & Oak Ave",
      city: "Chicago",
      state: "IL",
      zipCode: "60601",
      coordinates: {
        lat: 41.8781,
        lng: -87.6298
      }
    },
    assignedDate: new Date('2024-06-17').toISOString(),
    notes: [
      {
        id: uuidv4(),
        claimId: "claim-id",
        authorId: mockAdjusters[0].id,
        authorName: "Emily Carter",
        content: "Initial inspection completed. Damage assessment in-progress.",
        isInternal: false,
        createdAt: new Date('2024-06-18')
      }
    ],
    documents: [
      {
        id: uuidv4(),
        claimId: "claim-id",
        name: "Police Report",
        type: "PDF",
        url: "/documents/police-report-001234.pdf",
        uploadedBy: "Robert Anderson",
        uploadedAt: new Date('2024-06-16')
      }
    ],
    createdAt: new Date('2024-06-16'),
    updatedAt: new Date('2024-06-20')
  },
  {
    id: uuidv4(),
    claimNumber: "CLM-2024-001235",
    carrierId: mockCarriers[1].id,
    adjusterId: mockAdjusters[1].id,
    policyNumber: "AS-POL-456789",
    policyHolderName: "Maria Gonzalez",
    policyHolderPhone: "(*************",
    policyHolderEmail: "<EMAIL>",
    incidentDate: new Date('2024-06-10').toISOString(),
    reportedDate: new Date('2024-06-11').toISOString(),
    claimType: "Property",
    category: "Water Damage",
    status: "denied",
    priority: "high",
    description: "Burst pipe caused flooding in basement",
    damageDescription: "Basement flooding, damaged flooring, walls, and personal property",
    estimatedAmount: 15000,
    deductible: 1000,
    location: {
      id: uuidv4(),
      street: "456 Elm Street",
      city: "Northbrook",
      state: "IL",
      zipCode: "60062"
    },
    assignedDate: new Date('2024-06-12').toISOString(),
    notes: [],
    documents: [],
    createdAt: new Date('2024-06-11'),
    updatedAt: new Date('2024-06-19')
  }
];

export function ClaimPage() {
  const [claims, setClaims] = useState<Claim[]>(mockClaims);
  const [isClaimFormDialogOpen, setIsClaimFormDialogOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [editingClaim, setEditingClaim] = useState<Claim | null>(null);
  const [viewingClaim, setViewingClaim] = useState<Claim | null>(null);
  const [deletingClaim, setDeletingClaim] = useState<Claim | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-blue-100 text-blue-800';
      case 'in-progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'under-review':
        return 'bg-purple-100 text-purple-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'denied':
        return 'bg-red-100 text-red-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCarrierName = (carrierId: string) => {
    const carrier = mockCarriers.find(c => c.id === carrierId);
    return carrier ? carrier.name : 'Unknown';
  };

  const getAdjusterName = (adjusterId?: string) => {
    if (!adjusterId) return 'Unassigned';
    const adjuster = mockAdjusters.find(a => a.id === adjusterId);
    return adjuster ? `${adjuster.firstName} ${adjuster.lastName}` : 'Unknown';
  };

  const columns: Column<Claim>[] = [
    {
      key: 'claimNumber',
      header: 'Claim Number',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center gap-3">
          <div>
            <div className="font-medium">{value}</div>
            <div className="text-sm text-muted-foreground">{row.policyNumber}</div>
          </div>
        </div>
      )
    },
    {
      key: 'policyHolderName',
      header: 'Policy Holder',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-muted-foreground">{row.claimType}</div>
        </div>
      )
    },
    {
      key: 'carrierId',
      header: 'Carrier',
      render: (value) => (
        <div className="flex items-center gap-2">
          <Building2 className="h-4 w-4" />
          <span className="text-sm">{getCarrierName(value)}</span>
        </div>
      )
    },
    {
      key: 'adjusterId',
      header: 'Adjuster',
      render: (value) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4" />
          <span className="text-sm">{getAdjusterName(value)}</span>
        </div>
      )
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      render: (value) => (
        <Badge className={getStatusColor(value)}>
          {value}
        </Badge>
      )
    },
    {
      key: 'priority',
      header: 'Priority',
      sortable: true,
      render: (value) => (
        <Badge className={getPriorityColor(value)}>
          {value}
        </Badge>
      )
    },
    {
      key: 'estimatedAmount',
      header: 'Estimated Amount',
      sortable: true,
      render: (value) => (
        <div className="flex items-center justify-center gap-2">
          <DollarSign className="h-4 w-4" />
          <span className="font-medium">${value.toLocaleString()}</span>
        </div>
      )
    },
    {
      key: 'incidentDate',
      header: 'Incident Date',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="h-3 w-3" />
          {format(new Date(value), 'MMM dd, yyyy')}
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setEditingClaim(row);
              setIsClaimFormDialogOpen(true);
            }}
            className='cursor-pointer'
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setDeletingClaim(row);
              setIsDeleteModalOpen(true);
            }}
            className='cursor-pointer'
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleSave = (formData: any) => {
    setClaims(prev => [...prev, { ...formData, id: uuidv4() }]);
    setIsClaimFormDialogOpen(false);
    console.log('Form data: ', formData);
  }

  const handleEdit = (formData: any) => {
    console.log('Edit claim: ', formData);
    setClaims(claims.map(c =>
      c.id === formData.id ? { ...c, ...formData } : c
    ));
    setIsClaimFormDialogOpen(false);
  };

  const handleView = (claim: Claim) => {
    setViewingClaim(claim);
    setIsViewModalOpen(true);
  };

  const handleDelete = (claim: Claim) => {
    setClaims(claims.filter(c => c.id !== claim.id));
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Claims Management"
        description="Manage insurance claims, assignments, and processing"
      >
        <Button onClick={() => {
          setEditingClaim(null);
          setIsClaimFormDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          New Claim
        </Button>
      </PageHeader>

      <PageContent>
        <DataTable
          data={claims}
          columns={columns}
          searchPlaceholder="Search claims..."
          onRowClick={(claim) => handleView(claim)}
        />
      </PageContent>

      {/* Add/Edit Modal */}
      <Dialog open={isClaimFormDialogOpen} onOpenChange={setIsClaimFormDialogOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {editingClaim ? 'Edit Claim' : 'Add New Claim'}
            </DialogTitle>
          </DialogHeader>
          {
            editingClaim ? (
              <ClaimForm
                initialData={editingClaim}
                onCancel={() => setIsClaimFormDialogOpen(false)}
                onSubmit={handleEdit}
              />
            ) : (
              <ClaimForm
                onCancel={() => setIsClaimFormDialogOpen(false)}
                onSubmit={handleSave}
              />
            )
          }
        </DialogContent>
      </Dialog>

      { /* Confirm Delete Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              Delete Claim
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete this claim?</p>
          </div>
          <DialogFooter>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => {
                setDeletingClaim(null);
                setIsDeleteModalOpen(false)
              }}>
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => {
                  if (deletingClaim) handleDelete(deletingClaim);
                  setIsDeleteModalOpen(false);
                }}
              >
                Delete
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Modal */}
      {viewingClaim && (
        <Modal
          isOpen={isViewModalOpen}
          onClose={() => setIsViewModalOpen(false)}
          title={`Claim Details - ${viewingClaim.claimNumber}`}
          size="xl"
        >
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Claim Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Claim Number:</span>
                      <span className="font-medium">{viewingClaim.claimNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Policy Number:</span>
                      <span className="font-medium">{viewingClaim.policyNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status:</span>
                      <Badge className={getStatusColor(viewingClaim.status)}>
                        {viewingClaim.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Priority:</span>
                      <Badge className={getPriorityColor(viewingClaim.priority)}>
                        {viewingClaim.priority}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Financial Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Estimated Amount:</span>
                      <span className="font-medium">${viewingClaim.estimatedAmount.toLocaleString()}</span>
                    </div>
                    {viewingClaim.approvedAmount && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Approved Amount:</span>
                        <span className="font-medium">${viewingClaim.approvedAmount.toLocaleString()}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Deductible:</span>
                      <span className="font-medium">${viewingClaim.deductible.toLocaleString()}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Policy Holder Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name:</span>
                    <span className="font-medium">{viewingClaim.policyHolderName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Phone:</span>
                    <span className="font-medium">{viewingClaim.policyHolderPhone}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Email:</span>
                    <span className="font-medium">{viewingClaim.policyHolderEmail}</span>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notes" className="space-y-4">
              <div className="text-center text-muted-foreground py-8">
                No notes available for this claim.
              </div>
            </TabsContent>

            <TabsContent value="documents" className="space-y-4">
              <div className="text-center text-muted-foreground py-8">
                No documents uploaded for this claim.
              </div>
            </TabsContent>
          </Tabs>
        </Modal>
      )}
    </div>
  );
}