import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/custom-ui/page-header';
import { DataTable, type Column } from '@/components/custom-ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/custom-ui/badge';
import { Modal } from '@/components/custom-ui/modal';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/custom-ui/textarea';
import { Plus, Edit, Trash2, FolderTree, Folder, FileText } from 'lucide-react';
import type { Category } from '@/types';

// Mock Data
import { v4 as uuidv4 } from 'uuid';
export const mockCategories: Category[] = [
  {
    id: uuidv4(),
    name: "<PERSON> Claims",
    type: "claim",
    description: "All automotive related claims",
    isActive: true,
    sortOrder: 1,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: uuidv4(),
    name: "Collision",
    type: "claim",
    parentId: "auto-claims-id",
    description: "Vehicle collision claims",
    isActive: true,
    sortOrder: 1,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: uuidv4(),
    name: "Property Claims",
    type: "claim",
    description: "Property damage claims",
    isActive: true,
    sortOrder: 2,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: uuidv4(),
    name: "Water Damage",
    type: "damage",
    description: "Water related damage",
    isActive: true,
    sortOrder: 1,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  }
];

export function Categories() {
  const [categories, setCategories] = useState<Category[]>(mockCategories);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState<Partial<Category>>({});

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'claim':
        return <FileText className="h-4 w-4" />;
      case 'service':
        return <FolderTree className="h-4 w-4" />;
      case 'damage':
        return <Folder className="h-4 w-4" />;
      default:
        return <Folder className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'claim':
        return 'bg-blue-100 text-blue-800';
      case 'service':
        return 'bg-green-100 text-green-800';
      case 'damage':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns: Column<Category>[] = [
    {
      key: 'name',
      header: 'Category Name',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center gap-3">
          <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getTypeColor(row.type)}`}>
            {getTypeIcon(row.type)}
          </div>
          <div>
            <div className="font-medium">{value}</div>
            {row.parentId && (
              <div className="text-sm text-muted-foreground">Subcategory</div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'type',
      header: 'Type',
      sortable: true,
      render: (value) => (
        <Badge variant="secondary" className="capitalize">
          {value}
        </Badge>
      )
    },
    {
      key: 'description',
      header: 'Description',
      render: (value) => (
        <span className="text-sm text-muted-foreground line-clamp-2">
          {value}
        </span>
      )
    },
    {
      key: 'sortOrder',
      header: 'Sort Order',
      sortable: true,
      render: (value) => (
        <span className="font-mono text-sm">{value}</span>
      )
    },
    {
      key: 'isActive',
      header: 'Status',
      render: (value) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDelete(row.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleAdd = () => {
    setEditingCategory(null);
    setFormData({});
    setIsModalOpen(true);
  };

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setFormData(category);
    setIsModalOpen(true);
  };

  const handleDelete = (id: string) => {
    setCategories(categories.filter(c => c.id !== id));
  };

  const handleSave = () => {
    if (editingCategory) {
      // Update existing category
      setCategories(categories.map(c =>
        c.id === editingCategory.id ? { ...c, ...formData } : c
      ));
    } else {
      // Add new category
      const newCategory: Category = {
        id: Date.now().toString(),
        name: formData.name || '',
        type: formData.type as 'claim' | 'service' | 'damage' || 'claim',
        parentId: formData.parentId,
        description: formData.description || '',
        isActive: formData.isActive ?? true,
        sortOrder: formData.sortOrder || 1,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setCategories([...categories, newCategory]);
    }
    setIsModalOpen(false);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Categories"
        description="Manage claim categories, service types, and damage classifications"
      >
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Category
        </Button>
      </PageHeader>

      <PageContent>
        <DataTable
          data={categories}
          columns={columns}
          searchPlaceholder="Search categories..."
          onRowClick={(category) => console.log('View category:', category)}
        />
      </PageContent>

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingCategory ? 'Edit Category' : 'Add New Category'}
        size="md"
        footer={
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {editingCategory ? 'Update' : 'Create'}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Category Name</Label>
            <Input
              id="name"
              value={formData.name || ''}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter category name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Type</Label>
            <Select
              value={formData.type || ''}
              onValueChange={(value) => setFormData({ ...formData, type: value as 'claim' | 'service' | 'damage' })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select category type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="claim">Claim</SelectItem>
                <SelectItem value="service">Service</SelectItem>
                <SelectItem value="damage">Damage</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Enter category description"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="sortOrder">Sort Order</Label>
            <Input
              id="sortOrder"
              type="number"
              min="1"
              value={formData.sortOrder || ''}
              onChange={(e) => setFormData({ ...formData, sortOrder: parseInt(e.target.value) })}
              placeholder="Enter sort order"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive ?? true}
              onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
            />
            <Label htmlFor="isActive">Active</Label>
          </div>
        </div>
      </Modal>
    </div>
  );
}
